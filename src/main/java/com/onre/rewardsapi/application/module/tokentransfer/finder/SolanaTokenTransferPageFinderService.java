package com.onre.rewardsapi.application.module.tokentransfer.finder;

import com.onre.rewardsapi.application.common.finder.BaseFinderService;
import com.onre.rewardsapi.application.module.tokentransfer.exception.SolanaTokenTransferPageNotFoundException;
import com.onre.rewardsapi.domain.solanatokentransferpage.SolanaTokenTransferPage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
public class SolanaTokenTransferPageFinderService extends BaseFinderService<SolanaTokenTransferPage> {

    protected SolanaTokenTransferPageFinderService(JpaRepository<SolanaTokenTransferPage, UUID> repository) {
        super(repository);
    }

    @Override
    protected RuntimeException createNotFoundException(String message) {
        return new SolanaTokenTransferPageNotFoundException(message);
    }

    @Override
    protected Class<SolanaTokenTransferPage> getEntityType() {
        return SolanaTokenTransferPage.class;
    }
}
