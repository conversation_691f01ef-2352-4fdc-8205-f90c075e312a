package com.onre.rewardsapi.application.module.tokentransfer.port.out;

import java.math.BigInteger;
import java.time.Instant;
import java.util.List;

public interface GetTokenTransfers {
    Result invoke(Input input);

    record Input(
            String tokenAddress,
            Integer page,
            Integer pageSize
    ) {
    }

    record Result(
            List<Transaction> transactions
    ) {
        public record Transaction(
                String signature,
                String fromSolAddress,
                String toSolAddress,
                String fromTokenAccount,
                String toTokenAccount,
                BigInteger amount,
                Instant timestamp
        ) {
        }
    }
}
