package com.onre.rewardsapi.application.module.tokentransfer.exception;

import com.onre.rewardsapi.infrastructure.exception.ApiException;
import com.onre.rewardsapi.infrastructure.exception.ErrorType;

public class SolanaTokenTransferPageNotFoundException extends ApiException {

    public SolanaTokenTransferPageNotFoundException(String message) {
        super(message);
    }

    @Override
    protected ErrorType getErrorType() {
        return SolanaTokenTransferPageErrorType.SOLANA_TOKEN_TRANSFER_PAGE_NOT_FOUND;
    }

    @Override
    protected Module getModule() {
        return Module.TOKEN_TRANSFER;
    }
}
