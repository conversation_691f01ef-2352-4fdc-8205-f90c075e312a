package com.onre.rewardsapi.application.common.finder;

import com.onre.rewardsapi.application.common.util.CollectionUtil;
import com.onre.rewardsapi.domain.DomainEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Base finder service that provides common operations for finding domain entities.
 * This abstract class should be extended by specific finder services for each entity type.
 *
 * @param <T> the type of domain entity this finder service handles
 */
@NoRepositoryBean
public abstract class BaseFinderService<T extends DomainEntity> {
    
    private final JpaRepository<T, UUID> repository;
    
    protected BaseFinderService(JpaRepository<T, UUID> repository) {
        this.repository = repository;
    }
    
    /**
     * Abstract method that must be implemented by subclasses to provide error handling.
     * This method should throw an appropriate exception when an entity is not found.
     *
     * @param message the error message
     * @throws RuntimeException the specific exception type for the entity
     */
    protected abstract RuntimeException createNotFoundException(String message);
    
    /**
     * Abstract method that must be implemented by subclasses to provide the entity class.
     * This is used for generating meaningful error messages.
     *
     * @return the Class object representing the entity type
     */
    protected abstract Class<T> getEntityType();
    
    /**
     * Gets the simple name of the entity class for error messages.
     *
     * @return the simple class name
     */
    private String getEntityName() {
        return getEntityType().getSimpleName();
    }
    
    /**
     * Finds an entity by its ID.
     *
     * @param id the entity ID
     * @return an Optional containing the entity if found, empty otherwise
     */
    @Transactional(readOnly = true)
    public Optional<T> findById(UUID id) {
        return repository.findById(id);
    }
    
    /**
     * Checks if an entity exists by its ID.
     *
     * @param id the entity ID
     * @return true if the entity exists, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean existsById(UUID id) {
        return repository.existsById(id);
    }
    
    /**
     * Gets an entity by its ID, throwing an exception if not found.
     *
     * @param id the entity ID
     * @return the entity
     * @throws RuntimeException if the entity is not found
     */
    @Transactional(readOnly = true)
    public T getById(UUID id) {
        return repository.findById(id)
                .orElseThrow(() -> createNotFoundException(
                        "Entity " + getEntityName() + " with id: " + id + " not found!"
                ));
    }
    
    /**
     * Finds all entities with the given IDs.
     *
     * @param ids the set of entity IDs
     * @return a list of found entities (may be empty if no IDs provided)
     */
    @Transactional(readOnly = true)
    public List<T> findAllByIds(Set<UUID> ids) {
        if (ids.isEmpty()) {
            return Collections.emptyList();
        }
        return repository.findAllById(ids);
    }
    
    /**
     * Gets all entities with the given IDs, throwing an exception if any are not found.
     *
     * @param ids the set of entity IDs
     * @return a list of entities
     * @throws RuntimeException if any entities are not found
     */
    @Transactional(readOnly = true)
    public List<T> getAllByIds(Set<UUID> ids) {
        List<T> result = repository.findAllById(ids);
        
        if (ids.size() != result.size()) {
            Set<UUID> resultIds = CollectionUtil.mapToSet(result, DomainEntity::getId);
            Set<UUID> missing = ids.stream()
                    .filter(id -> !resultIds.contains(id))
                    .collect(Collectors.toSet());
            
            throw createNotFoundException(
                    "Entities " + getEntityName() + " with ids: [" + 
                    missing.stream().map(UUID::toString).collect(Collectors.joining(", ")) + 
                    "] not found!"
            );
        }
        
        return result;
    }
    
    /**
     * Finds all entities.
     *
     * @return a list of all entities
     */
    @Transactional(readOnly = true)
    public List<T> findAll() {
        return repository.findAll();
    }
}
