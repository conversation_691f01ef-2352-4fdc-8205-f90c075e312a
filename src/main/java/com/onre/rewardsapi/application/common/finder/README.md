# Common Finder Service

This package provides a base finder service that can be extended by specific entity finder services to provide common database operations.

## Overview

The `BaseFinderService<T>` is an abstract class that provides common operations for finding domain entities. It follows the repository pattern and provides type-safe operations for entity retrieval.

## Features

- **Type-safe operations**: All operations are type-safe and work with any entity that extends `DomainEntity`
- **Consistent error handling**: Abstract method for creating appropriate exceptions
- **Transaction management**: All operations are properly annotated with `@Transactional(readOnly = true)`
- **Null safety**: Uses `Optional` for operations that might not find entities
- **Batch operations**: Supports finding multiple entities by IDs with validation

## Usage

### 1. Create a Finder Service

Extend `BaseFinderService<T>` for your entity:

```java
@Service
public class UserFinderService extends BaseFinderService<User> {
    
    public UserFinderService(UserRepository repository) {
        super(repository);
    }
    
    @Override
    protected RuntimeException createNotFoundException(String message) {
        return new UserNotFoundException(message);
    }
    
    @Override
    protected Class<User> getEntityType() {
        return User.class;
    }
}
```

### 2. Use the Finder Service

Inject and use the finder service in your application:

```java
@Service
public class UserService {
    
    private final UserFinderService userFinderService;
    
    public UserService(UserFinderService userFinderService) {
        this.userFinderService = userFinderService;
    }
    
    public User getUserById(UUID id) {
        return userFinderService.getById(id); // Throws exception if not found
    }
    
    public Optional<User> findUserById(UUID id) {
        return userFinderService.findById(id); // Returns Optional
    }
    
    public List<User> getUsersByIds(Set<UUID> ids) {
        return userFinderService.getAllByIds(ids); // Throws exception if any not found
    }
}
```

## Available Methods

### Single Entity Operations

- `Optional<T> findById(UUID id)` - Returns Optional, never throws
- `boolean existsById(UUID id)` - Checks if entity exists
- `T getById(UUID id)` - Returns entity or throws exception

### Multiple Entity Operations

- `List<T> findAllByIds(Set<UUID> ids)` - Returns found entities (may be partial)
- `List<T> getAllByIds(Set<UUID> ids)` - Returns all entities or throws exception
- `List<T> findAll()` - Returns all entities

## Error Handling

The `createNotFoundException(String message)` method must be implemented to provide appropriate exception handling for your domain. This allows each entity type to have its own specific exception type while maintaining consistent behavior.

## Dependencies

- Spring Data JPA
- Spring Transaction Management
- Java 17+

## Example Implementation

See `TokenTransferFinderService` for a complete example implementation.
