package com.onre.rewardsapi.application.common.util;

import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class CollectionUtil {
    
    /**
     * Maps a collection to a set using the provided mapper function.
     * This is the Java equivalent of <PERSON><PERSON><PERSON>'s mapToSet extension function.
     *
     * @param collection the source collection
     * @param mapper the function to apply to each element
     * @param <T> the type of elements in the source collection
     * @param <R> the type of elements in the resulting set
     * @return a set containing the mapped elements
     */
    public static <T, R> Set<R> mapToSet(Collection<T> collection, Function<T, R> mapper) {
        return collection.stream()
                .map(mapper)
                .collect(Collectors.toSet());
    }
}
