package com.onre.rewardsapi.domain.tokenbalance;

import com.onre.rewardsapi.domain.tokenbalance.repository.TokenBalanceRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.time.Instant;

@Service
@RequiredArgsConstructor
public class OneTokenBalanceCreateService {

    private final TokenBalanceRepository tokenBalanceRepository;

    @Transactional
    public OneTokenBalance create(String address, BigInteger balance, Instant timestamp) {
        return tokenBalanceRepository.save(OneTokenBalance.create(
                address,
                balance,
                timestamp
        ));
    }
}
