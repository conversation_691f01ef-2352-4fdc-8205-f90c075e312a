package com.onre.rewardsapi.domain.solanatokentransferpage;

import com.onre.rewardsapi.domain.solanatokentransferpage.repository.SolanaTokenTransferPageRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class SolanaTokenTransferPageCreateService {

    private final SolanaTokenTransferPageRepository solanaTokenTransferPageRepository;

    @Transactional
    public SolanaTokenTransferPage create(Integer lastProcessedPage, String lastProcessedTransactionSignature, Boolean completePageProcessed) {
        return solanaTokenTransferPageRepository.save(SolanaTokenTransferPage.create(
                lastProcessedPage,
                lastProcessedTransactionSignature,
                completePageProcessed
        ));
    }

}
