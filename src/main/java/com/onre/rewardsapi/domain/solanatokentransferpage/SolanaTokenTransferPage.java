package com.onre.rewardsapi.domain.solanatokentransferpage;

import com.onre.rewardsapi.domain.UpdatableEntity;
import jakarta.persistence.Entity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SolanaTokenTransferPage extends UpdatableEntity {

    private Integer lastProcessedPage;
    private String lastProcessedTransactionSignature;
    private Boolean completePageProcessed;

    public static SolanaTokenTransferPage create(
            Integer lastProcessedPage,
            String lastProcessedTransactionSignature,
            Boolean completePageProcessed) {
        return new SolanaTokenTransferPage(
                lastProcessedPage,
                lastProcessedTransactionSignature,
                completePageProcessed
        );
    }

    public void updatePageInfo(
            Integer lastProcessedPage,
            String lastProcessedTransactionSignature,
            Boolean completePageProcessed) {
        this.lastProcessedPage = lastProcessedPage;
        this.lastProcessedTransactionSignature = lastProcessedTransactionSignature;
        this.completePageProcessed = completePageProcessed;
    }
}
