package com.onre.rewardsapi.infrastructure.exception;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;

public abstract class ApiException extends RuntimeException {
    protected abstract ErrorType getErrorType();


    protected ApiException(String message) {
        super(message);
    }

    /**
     * Use to obtain information about module to which exception belongs.
     */
    protected abstract Module getModule();

    /**
     * Use to obtain microservice-wide unique code representing the error.
     * Could be used for error handling by the clients.
     */
    @Override
    public String getMessage() {
        return Optional.ofNullable(getErrorType().getMessage()).orElse(getModule().getErrorMessage());
    }

    /**
     * Additional parameters
     *
     * @return Add additional parameters
     */
    public Map<String, String> getParameters() {
        return Collections.emptyMap();
    }

    /**
     * Build exception code
     *
     * @return Exception code
     */
    public String getErrorCode() {
        return getModule().getErrorCode() + getErrorType().getCode();
    }

    /**
     * Should this exception be logged or ignored
     *
     * @return Should be logged
     */
    public boolean shouldBeLogged() {
        return true;
    }

    @Getter
    @RequiredArgsConstructor
    public enum Module {
        TOKEN_TRANSFER("100", "Block module");

        /**
         * Error custom code
         */
        private final String errorCode;

        /**
         * Error custom message
         */
        private final String errorMessage;
    }
}
