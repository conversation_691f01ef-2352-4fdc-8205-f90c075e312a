package com.onre.rewardsapi.adapter.in.quartz.jobs;

import com.onre.rewardsapi.adapter.in.quartz.JobType;
import com.onre.rewardsapi.adapter.in.quartz.QuartzJob;
import com.onre.rewardsapi.application.common.command.Command;
import com.onre.rewardsapi.application.module.tokentransfer.command.ProcessTokenTransferCommand;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.springframework.stereotype.Component;

@Component
@DisallowConcurrentExecution
public class SolanaBlockScanJob extends QuartzJob {
    @Override
    public JobType getJobType() {
        return JobType.SOLSCAN_TOKEN_TRANSFER;
    }

    @Override
    public Command<?> createCommand(JobExecutionContext context) {
        return new ProcessTokenTransferCommand();
    }
}
