package com.onre.rewardsapi.adapter.out.solscan;

import com.onre.rewardsapi.adapter.out.common.BaseConnector;
import com.onre.rewardsapi.adapter.out.solscan.properties.SolscanProperties;
import com.onre.rewardsapi.adapter.out.solscan.response.SolscanTokenTransferResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class SolscanApiConnector extends BaseConnector {
    private final SolscanProperties solscanProperties;

    private static final String DEFAULT_SORT_BY = "block_time";
    private static final String ASC_ORDER = "asc";

    protected SolscanApiConnector(SolscanProperties solscanProperties) {
        super(solscanProperties.baseUrl());
        this.solscanProperties = solscanProperties;
    }

    public SolscanTokenTransferResponse getTokenTransfers(String address, Integer page, Integer pageSize) {
        return retrieveResponseWithErrorHandler(
                restClient.get()
                        .uri(uriBuilder -> uriBuilder.path("/token/transfer")
                                .queryParam("address", address)
                                .queryParam("exclude_amount_zero", true)
                                .queryParam("page", page)
                                .queryParam("page_size", pageSize)
                                .queryParam("sort_by", DEFAULT_SORT_BY)
                                .queryParam("sort_order", ASC_ORDER)
                                .build()
                        )
                        .header("token", solscanProperties.apiKey())
                        .accept(MediaType.APPLICATION_JSON),
                SolscanTokenTransferResponse.class
        );
    }
}
