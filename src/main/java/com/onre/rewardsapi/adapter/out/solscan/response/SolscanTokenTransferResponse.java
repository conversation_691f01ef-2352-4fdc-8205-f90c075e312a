package com.onre.rewardsapi.adapter.out.solscan.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigInteger;
import java.time.Instant;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public record SolscanTokenTransferResponse(
        Boolean success,
        List<TokenTransfer> data
) {
    @JsonIgnoreProperties(ignoreUnknown = true)
    public record TokenTransfer(
            @JsonProperty("trans_id")
            String transactionSignature,
            @JsonProperty("from_address")
            String fromAddress,
            @JsonProperty("to_address")
            String toAddress,
            @JsonProperty("from_token_account")
            String fromTokenAccount,
            @JsonProperty("to_token_account")
            String toTokenAccount,
            @JsonProperty("amount")
            BigInteger amount,
            @JsonProperty("time")
            Instant time
    ) {
    }
}