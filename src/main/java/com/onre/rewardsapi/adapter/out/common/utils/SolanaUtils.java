package com.onre.rewardsapi.adapter.out.common.utils;

import lombok.NoArgsConstructor;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class SolanaUtils {
    public static final String SOLANA_DEFAULT_ADDRESS = "11111111111111111111111111111111";

    public static String addressOrDefault(String address) {
        return address == null || address.isBlank()
                ? SOLANA_DEFAULT_ADDRESS
                : address;
    }
}
