package com.onre.rewardsapi.adapter.out.solscan;

import com.onre.rewardsapi.adapter.out.common.utils.SolanaUtils;
import com.onre.rewardsapi.adapter.out.solscan.response.SolscanTokenTransferResponse;
import com.onre.rewardsapi.application.module.tokentransfer.port.out.GetTokenTransfers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GetTokenTransferSolscan implements GetTokenTransfers {
    private final SolscanApiConnector solscanApiConnector;

    @Override
    public Result invoke(Input input) {

        SolscanTokenTransferResponse tokenTransfersResponse = solscanApiConnector.getTokenTransfers(
                input.tokenAddress(),
                input.page(),
                input.pageSize()
        );

        if (!tokenTransfersResponse.success()) {
            throw new IllegalStateException("Solscan API returned error.");
        }

        return new Result(tokenTransfersResponse.data().stream().map(tokenTransfer -> new Result.Transaction(
                tokenTransfer.transactionSignature(),
                SolanaUtils.addressOrDefault(tokenTransfer.fromAddress()),
                SolanaUtils.addressOrDefault(tokenTransfer.toAddress()),
                SolanaUtils.addressOrDefault(tokenTransfer.fromTokenAccount()),
                SolanaUtils.addressOrDefault(tokenTransfer.toTokenAccount()),
                tokenTransfer.amount(),
                tokenTransfer.time()
        )).toList());
    }
}


