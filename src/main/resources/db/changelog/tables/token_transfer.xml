<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
    http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">


    <changeSet id="token_transfer_table_creation" author="Marian-<PERSON>" runOnChange="false">
        <preConditions onSqlOutput="TEST" onFail="CONTINUE">
            <not>
                <tableExists tableName="token_transfer"/>
            </not>
        </preConditions>

        <createTable tableName="token_transfer">
            <column name="id" type="uuid">
                <constraints primaryKey="true" primaryKeyName="PK_token_transfer"/>
            </column>
            <column name="from_sol_address" type="bytea">
                <constraints nullable="false"/>
            </column>
            <column name="to_sol_address" type="bytea">
                <constraints nullable="false"/>
            </column>
            <column name="transaction_signature" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="amount" type="numeric"/>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="token_transfer_transaction_signature_unique_index" author="Marian-Daniel Rolnik">
        <createIndex tableName="token_transfer" indexName="token_transfer_transaction_signature_unique_index">
            <column name="transaction_signature"/>
        </createIndex>
    </changeSet>

    <changeSet id="token_transfer_add_timestamp_column" author="Marian-Daniel Rolnik">
        <addColumn tableName="token_transfer">
            <column name="timestamp" type="timestamptz"/>
        </addColumn>
    </changeSet>

    <changeSet id="add_token_transfer_token_accounts_columns" author="Marian-Daniel Rolnik">
        <addColumn tableName="token_transfer">
            <column name="from_sol_token_account" type="bytea">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="token_transfer">
            <column name="to_sol_token_account" type="bytea">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="convert_sol_address_columns_to_text" author="Marian-Daniel Rolnik">
        <comment>Convert bytea columns to text for Solana addresses</comment>

        <!-- Convert from_sol_address from bytea to text -->
        <modifyDataType tableName="token_transfer" columnName="from_sol_address" newDataType="text"/>

        <!-- Convert to_sol_address from bytea to text -->
        <modifyDataType tableName="token_transfer" columnName="to_sol_address" newDataType="text"/>

        <!-- Convert from_sol_token_account from bytea to text -->
        <modifyDataType tableName="token_transfer" columnName="from_sol_token_account" newDataType="text"/>

        <!-- Convert to_sol_token_account from bytea to text -->
        <modifyDataType tableName="token_transfer" columnName="to_sol_token_account" newDataType="text"/>
    </changeSet>

</databaseChangeLog>
