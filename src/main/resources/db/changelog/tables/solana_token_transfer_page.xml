<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
    http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">


    <changeSet id="create_solana_token_transfer_page_table" author="Marian-<PERSON>" runOnChange="false">
        <preConditions onSqlOutput="TEST" onFail="CONTINUE">
            <not>
                <tableExists tableName="solana_token_transfer_page"/>
            </not>
        </preConditions>

        <createTable tableName="solana_token_transfer_page">
            <column name="id" type="uuid">
                <constraints primaryKey="true" primaryKeyName="PK_solana_token_transfer_page"/>
            </column>
            <column name="last_processed_page" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="last_processed_transaction_signature" type="text"/>
            <column name="complete_page_processed" type="boolean">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
        </createTable>

    </changeSet>
</databaseChangeLog>