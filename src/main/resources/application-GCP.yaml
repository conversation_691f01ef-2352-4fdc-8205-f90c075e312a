##################################################
#   Google Cloud specific configuration          #
##################################################

spring:

  datasource:
    url: ************************************************=${DB_INSTANCE_CONNECTION_NAME}&socketFactory=com.google.cloud.sql.postgres.SocketFactory&ipTypes=PRIVATE&reWriteBatchedInserts=true
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}

  quartz:
    properties:
      org:
        quartz:
          dataSource:
            dataSource:
              URL: ************************************************=${DB_INSTANCE_CONNECTION_NAME}&socketFactory=com.google.cloud.sql.postgres.SocketFactory&ipTypes=PRIVATE
              user: ${DB_USERNAME}
              password: ${DB_PASSWORD}
