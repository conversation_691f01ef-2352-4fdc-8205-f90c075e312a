package com.onre.rewardsapi.common;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;

import java.lang.reflect.Field;

@SpringBootTest
public class IntegrationTest {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    public void beforeEach() {
        jdbcTemplate.execute(TRUNCATE_ALL_TABLES_SQL);
    }

    @AfterEach
    public void afterEach() {
        // Clean up after each test
    }

    /**
     * Gets a private property from an object
     */
    @SuppressWarnings("unchecked")
    protected <T, R> R getPrivateProperty(T object, String name) {
        try {
            Field field = findField(object.getClass(), name);
            if (field != null) {
                field.setAccessible(true);
                return (R) field.get(object);
            }
            return null;
        } catch (Exception e) {
            throw new RuntimeException("Error accessing private property: " + name, e);
        }
    }

    /**
     * Sets a private property on an object
     */
    protected <T> void setPrivateProperty(T object, String name, Object value) {
        try {
            Field field = findField(object.getClass(), name);
            if (field != null) {
                field.setAccessible(true);
                field.set(object, value);
            }
        } catch (Exception e) {
            throw new RuntimeException("Error setting private property: " + name, e);
        }
    }

    private Field findField(Class<?> clazz, String name) {
        Class<?> searchType = clazz;
        while (searchType != null) {
            for (Field field : searchType.getDeclaredFields()) {
                if (name.equals(field.getName())) {
                    return field;
                }
            }
            searchType = searchType.getSuperclass();
        }
        return null;
    }

    private static final String TRUNCATE_ALL_TABLES_SQL = 
        "DO\n" +
        "$do$\n" +
        "\tBEGIN\n" +
        "\t\tEXECUTE\n" +
        "\t\t\t(SELECT 'TRUNCATE TABLE ' || string_agg(oid::regclass::text, ', ') || ' CASCADE'\n" +
        "\t\t\t FROM   pg_class\n" +
        "\t\t\t WHERE  relkind = 'r'  -- only tables\n" +
        "\t\t\t   AND    relnamespace = 'public'::regnamespace\n" +
        "\t\t\t   AND relname NOT IN ('databasechangelog', 'databasechangeloglock')\n" +
        "\t\t\t);\n" +
        "\tEND\n" +
        "$do$;";
}
