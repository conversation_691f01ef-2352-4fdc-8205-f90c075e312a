package com.onre.rewardsapi.application.module.tokentransfer;

import com.onre.rewardsapi.application.common.properties.TokenProperties;
import com.onre.rewardsapi.application.module.tokentransfer.command.ProcessTokenTransferCommand;
import com.onre.rewardsapi.application.module.tokentransfer.finder.SolanaTokenTransferPageFinderService;
import com.onre.rewardsapi.application.module.tokentransfer.port.out.GetTokenTransfers;
import com.onre.rewardsapi.domain.solanatokentransferpage.SolanaTokenTransferPage;
import com.onre.rewardsapi.domain.solanatokentransferpage.SolanaTokenTransferPageCreateService;
import com.onre.rewardsapi.domain.tokentransfer.TokenTransferCreateService;
import com.onre.rewardsapi.infrastructure.lock.AdvisoryLockService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigInteger;
import java.time.Instant;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProcessTokenTransfersCommandHandlerTest {

    @Mock
    private GetTokenTransfers getTokenTransfers;

    @Mock
    private AdvisoryLockService advisoryLockService;

    @Mock
    private SolanaTokenTransferPageFinderService solanaTokenTransferPageFinderService;

    @Mock
    private SolanaTokenTransferPageCreateService solanaTokenTransferPageCreateService;

    @Mock
    private TokenTransferCreateService tokenTransferCreateService;

    @Mock
    private TokenProperties tokenProperties;

    @Mock
    private TokenProperties.Token tokenConfig;

    @Mock
    private ProcessTokenTransfersCommandHandler selfMock;

    private ProcessTokenTransfersCommandHandler handler;

    private static final String TOKEN_ADDRESS = "5Y8NV33Vv7WbnLfq3zBcKSdYPrk7g2KoiQoe7M2tcxp5";
    private static final int DEFAULT_PAGE_SIZE = 100;

    @BeforeEach
    void setUp() {
        handler = new ProcessTokenTransfersCommandHandler(
                getTokenTransfers,
                advisoryLockService,
                solanaTokenTransferPageFinderService,
                solanaTokenTransferPageCreateService,
                tokenTransferCreateService,
                tokenProperties
        );

        // Set up the self reference using reflection
        ReflectionTestUtils.setField(handler, "self", selfMock);

        // Mock token properties
        when(tokenProperties.one()).thenReturn(tokenConfig);
        when(tokenConfig.address()).thenReturn(TOKEN_ADDRESS);
    }

    @Test
    void handle_FirstTimeProcessing_CreatesNewPageAndProcessesTransactions() {
        // Given
        ProcessTokenTransferCommand command = new ProcessTokenTransferCommand();
        
        // No existing pages
        when(solanaTokenTransferPageFinderService.findAll()).thenReturn(Collections.emptyList());
        
        // Create new page
        SolanaTokenTransferPage newPage = createMockPage(1, null, false);
        when(solanaTokenTransferPageCreateService.create(1, null, false)).thenReturn(newPage);
        
        // Mock API response with transactions
        List<GetTokenTransfers.Result.Transaction> transactions = createMockTransactions(3);
        GetTokenTransfers.Result result = new GetTokenTransfers.Result(transactions);
        when(getTokenTransfers.invoke(any(GetTokenTransfers.Input.class))).thenReturn(result);

        // When
        handler.handle(command);

        // Then
        verify(solanaTokenTransferPageCreateService).create(1, null, false);
        verify(getTokenTransfers).invoke(new GetTokenTransfers.Input(TOKEN_ADDRESS, 1, DEFAULT_PAGE_SIZE));
        verify(selfMock).saveTransactions(transactions);
        verify(selfMock).updateProcessedPage(1, "signature-2", false);
    }

    @Test
    void handle_ExistingCompletePageProcessed_StartsFromNextPage() {
        // Given
        ProcessTokenTransferCommand command = new ProcessTokenTransferCommand();
        
        // Existing complete page
        SolanaTokenTransferPage existingPage = createMockPage(5, "last-signature", true);
        when(solanaTokenTransferPageFinderService.findAll()).thenReturn(List.of(existingPage));
        
        // Mock API response with empty transactions (end of data)
        GetTokenTransfers.Result emptyResult = new GetTokenTransfers.Result(Collections.emptyList());
        when(getTokenTransfers.invoke(any(GetTokenTransfers.Input.class))).thenReturn(emptyResult);

        // When
        handler.handle(command);

        // Then
        verify(getTokenTransfers).invoke(new GetTokenTransfers.Input(TOKEN_ADDRESS, 6, DEFAULT_PAGE_SIZE));
        verify(selfMock).updateProcessedPage(5, null, false);
        verify(selfMock, never()).saveTransactions(any());
    }

    @Test
    void handle_ExistingIncompletePageProcessed_ContinuesFromSamePage() {
        // Given
        ProcessTokenTransferCommand command = new ProcessTokenTransferCommand();
        
        // Existing incomplete page
        SolanaTokenTransferPage existingPage = createMockPage(3, "existing-signature", false);
        when(solanaTokenTransferPageFinderService.findAll()).thenReturn(List.of(existingPage));
        
        // Mock API response with transactions including the last processed one
        List<GetTokenTransfers.Result.Transaction> transactions = List.of(
                createTransaction("existing-signature", "from1", "to1"),
                createTransaction("new-signature-1", "from2", "to2"),
                createTransaction("new-signature-2", "from3", "to3")
        );
        GetTokenTransfers.Result result = new GetTokenTransfers.Result(transactions);
        when(getTokenTransfers.invoke(any(GetTokenTransfers.Input.class))).thenReturn(result);

        // When
        handler.handle(command);

        // Then
        verify(getTokenTransfers).invoke(new GetTokenTransfers.Input(TOKEN_ADDRESS, 3, DEFAULT_PAGE_SIZE));
        
        // Should save only new transactions (excluding the existing signature)
        List<GetTokenTransfers.Result.Transaction> expectedTransactions = List.of(
                createTransaction("new-signature-1", "from2", "to2"),
                createTransaction("new-signature-2", "from3", "to3")
        );
        verify(selfMock).saveTransactions(expectedTransactions);
        verify(selfMock).updateProcessedPage(3, "new-signature-2", false);
    }

    @Test
    void handle_FullPageOfTransactions_ProcessesMultiplePages() {
        // Given
        ProcessTokenTransferCommand command = new ProcessTokenTransferCommand();
        
        // No existing pages
        when(solanaTokenTransferPageFinderService.findAll()).thenReturn(Collections.emptyList());
        
        SolanaTokenTransferPage newPage = createMockPage(1, null, false);
        when(solanaTokenTransferPageCreateService.create(1, null, false)).thenReturn(newPage);
        
        // First call returns full page (100 transactions)
        List<GetTokenTransfers.Result.Transaction> fullPageTransactions = createMockTransactions(DEFAULT_PAGE_SIZE);
        GetTokenTransfers.Result fullPageResult = new GetTokenTransfers.Result(fullPageTransactions);
        
        // Second call returns partial page (50 transactions)
        List<GetTokenTransfers.Result.Transaction> partialPageTransactions = createMockTransactions(50);
        GetTokenTransfers.Result partialPageResult = new GetTokenTransfers.Result(partialPageTransactions);
        
        when(getTokenTransfers.invoke(any(GetTokenTransfers.Input.class)))
                .thenReturn(fullPageResult)
                .thenReturn(partialPageResult);

        // When
        handler.handle(command);

        // Then
        verify(getTokenTransfers).invoke(new GetTokenTransfers.Input(TOKEN_ADDRESS, 1, DEFAULT_PAGE_SIZE));
        verify(getTokenTransfers).invoke(new GetTokenTransfers.Input(TOKEN_ADDRESS, 2, DEFAULT_PAGE_SIZE));
        verify(selfMock).saveTransactions(fullPageTransactions);
        verify(selfMock).saveTransactions(partialPageTransactions);
        verify(selfMock).updateProcessedPage(2, "signature-49", false);
    }

    private SolanaTokenTransferPage createMockPage(Integer lastProcessedPage, String lastProcessedSignature, Boolean completePageProcessed) {
        SolanaTokenTransferPage page = mock(SolanaTokenTransferPage.class);
        when(page.getLastProcessedPage()).thenReturn(lastProcessedPage);
        when(page.getLastProcessedTransactionSignature()).thenReturn(lastProcessedSignature);
        when(page.getCompletePageProcessed()).thenReturn(completePageProcessed);
        return page;
    }

    private List<GetTokenTransfers.Result.Transaction> createMockTransactions(int count) {
        return java.util.stream.IntStream.range(0, count)
                .mapToObj(i -> createTransaction("signature-" + i, "from-" + i, "to-" + i))
                .toList();
    }

    private GetTokenTransfers.Result.Transaction createTransaction(String signature, String fromAddress, String toAddress) {
        return new GetTokenTransfers.Result.Transaction(
                signature,
                fromAddress,
                toAddress,
                "fromTokenAccount",
                "toTokenAccount",
                BigInteger.valueOf(1000),
                Instant.now()
        );
    }
}
