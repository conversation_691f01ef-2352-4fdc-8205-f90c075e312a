package com.onre.rewardsapi.application.module.tokentransfer;

import com.onre.rewardsapi.application.common.properties.TokenProperties;
import com.onre.rewardsapi.application.module.tokentransfer.command.ProcessTokenTransferCommand;
import com.onre.rewardsapi.application.module.tokentransfer.finder.SolanaTokenTransferPageFinderService;
import com.onre.rewardsapi.application.module.tokentransfer.port.out.GetTokenTransfers;
import com.onre.rewardsapi.common.IntegrationTest;
import com.onre.rewardsapi.domain.solanatokentransferpage.SolanaTokenTransferPage;
import com.onre.rewardsapi.domain.solanatokentransferpage.SolanaTokenTransferPageCreateService;
import com.onre.rewardsapi.domain.tokentransfer.TokenTransferCreateService;
import com.onre.rewardsapi.infrastructure.lock.AdvisoryLockService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ContextConfiguration;

import java.math.BigInteger;
import java.time.Instant;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ContextConfiguration(classes = ProcessTokenTransfersCommandHandlerTest.TestConfig.class)
class ProcessTokenTransfersCommandHandlerTest extends IntegrationTest {

    @MockBean
    private GetTokenTransfers getTokenTransfers;

    @Autowired
    private SolanaTokenTransferPageFinderService solanaTokenTransferPageFinderService;

    @Autowired
    private SolanaTokenTransferPageCreateService solanaTokenTransferPageCreateService;

    @Autowired
    private TokenTransferCreateService tokenTransferCreateService;

    @Autowired
    private AdvisoryLockService advisoryLockService;

    @Autowired
    private ProcessTokenTransfersCommandHandler handler;

    @Autowired
    private TokenProperties tokenProperties;

    @Configuration
    static class TestConfig {
        @Bean
        @Primary
        public ProcessTokenTransfersCommandHandler processTokenTransfersCommandHandler(
                GetTokenTransfers getTokenTransfers,
                AdvisoryLockService advisoryLockService,
                SolanaTokenTransferPageFinderService solanaTokenTransferPageFinderService,
                SolanaTokenTransferPageCreateService solanaTokenTransferPageCreateService,
                TokenTransferCreateService tokenTransferCreateService,
                TokenProperties tokenProperties) {
            return new ProcessTokenTransfersCommandHandler(
                    getTokenTransfers,
                    advisoryLockService,
                    solanaTokenTransferPageFinderService,
                    solanaTokenTransferPageCreateService,
                    tokenTransferCreateService,
                    tokenProperties
            );
        }
    }

    private static final String TOKEN_ADDRESS = "5Y8NV33Vv7WbnLfq3zBcKSdYPrk7g2KoiQoe7M2tcxp5";
    private static final int DEFAULT_PAGE_SIZE = 100;

    @Test
    void handle_FirstTimeProcessing_CreatesNewPageAndProcessesTransactions() {
        // Given
        ProcessTokenTransferCommand command = new ProcessTokenTransferCommand();

        // Mock API response with transactions (partial page)
        List<GetTokenTransfers.Result.Transaction> transactions = createMockTransactions(3);
        GetTokenTransfers.Result result = new GetTokenTransfers.Result(transactions);
        when(getTokenTransfers.invoke(any(GetTokenTransfers.Input.class))).thenReturn(result);

        // When
        handler.handle(command);

        // Then
        verify(getTokenTransfers).invoke(new GetTokenTransfers.Input(TOKEN_ADDRESS, 1, DEFAULT_PAGE_SIZE));
    }

    @Test
    void handle_EmptyTransactionsResponse_StopsProcessing() {
        // Given
        ProcessTokenTransferCommand command = new ProcessTokenTransferCommand();

        // Mock API response with empty transactions (end of data)
        GetTokenTransfers.Result emptyResult = new GetTokenTransfers.Result(Collections.emptyList());
        when(getTokenTransfers.invoke(any(GetTokenTransfers.Input.class))).thenReturn(emptyResult);

        // When
        handler.handle(command);

        // Then
        verify(getTokenTransfers).invoke(new GetTokenTransfers.Input(TOKEN_ADDRESS, 1, DEFAULT_PAGE_SIZE));
    }

    @Test
    void getCommandType_ReturnsCorrectCommandClass() {
        // When
        Class<ProcessTokenTransferCommand> commandType = handler.getCommandType();

        // Then
        assertEquals(ProcessTokenTransferCommand.class, commandType);
    }

    private List<GetTokenTransfers.Result.Transaction> createMockTransactions(int count) {
        return java.util.stream.IntStream.range(0, count)
                .mapToObj(i -> createTransaction("signature-" + i, "from-" + i, "to-" + i))
                .toList();
    }

    private GetTokenTransfers.Result.Transaction createTransaction(String signature, String fromAddress, String toAddress) {
        return new GetTokenTransfers.Result.Transaction(
                signature,
                fromAddress,
                toAddress,
                "fromTokenAccount",
                "toTokenAccount",
                BigInteger.valueOf(1000),
                Instant.now()
        );
    }
}
